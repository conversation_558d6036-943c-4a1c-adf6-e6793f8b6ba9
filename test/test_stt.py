#!/usr/bin/env python3
"""
Test script for speech-to-text functionality using FastRTC.
This script allows testing the speech transcription feature from the terminal.
"""

import os
import sys
import time
import threading
import numpy as np
import sounddevice as sd
from fastrtc import get_stt_model

# Add the parent directory to the path so we can import from the main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the AudioProcessor class to test
from audio_processor import AudioProcessor
from utils.shared_state import SharedState

# Constants for audio recording
SAMPLE_RATE = 16000  # Hz
CHANNELS = 1
CHUNK_DURATION = 0.5  # seconds
RECORDING_DURATION = 10  # seconds in total

class MockAudioFrame:
    """Mock audio frame similar to what streamlit-webrtc would provide"""
    def __init__(self, audio_data):
        self.audio_data = audio_data
    
    def to_ndarray(self):
        return self.audio_data

class TestSharedState:
    """Simple mock of the SharedState class for testing"""
    def __init__(self):
        self.messages = []
    
    def set_user_response(self, text):
        print(f"📝 Adding message to shared state: '{text}'")
        self.messages.append({"role": "user", "content": text})
    
    def get_messages(self):
        return self.messages

def test_audio_processor():
    """Test the AudioProcessor with live audio input"""
    print("🎤 Testing AudioProcessor with live microphone input")
    
    # Create a mock shared state
    shared_state = TestSharedState()
    
    # Create an AudioProcessor instance
    audio_processor = AudioProcessor(
        shared_state=shared_state,
        enable_auto_send=True,
        silence_threshold=0.01,
        update_interval=1.0
    )
    
    print("\n🟢 Starting audio capture. Please speak clearly...")
    print("⏱️  Recording for", RECORDING_DURATION, "seconds...")
    
    # Start time
    start_time = time.time()
    
    # Process audio in chunks to simulate streaming
    def audio_callback(indata, frames, time_info, status):
        if status:
            print(f"⚠️  Status: {status}")
        
        # Create a mock frame and process it
        frame = MockAudioFrame(indata.copy())
        audio_processor.recv(frame)
        
        # Display audio level
        audio_level = np.abs(indata).mean()
        bars = int(audio_level * 100)
        print(f"\rAudio level: {'|' * bars}{' ' * (20 - bars)} [{audio_level:.4f}]", end="")
        
        # Show current transcript periodically
        current_transcript = audio_processor.get_transcript()
        if current_transcript:
            print(f"\r🗣️  Current transcript: {current_transcript}")

    # Start audio stream
    with sd.InputStream(
        samplerate=SAMPLE_RATE,
        channels=CHANNELS,
        callback=audio_callback,
        blocksize=int(SAMPLE_RATE * CHUNK_DURATION)
    ):
        # Wait for the recording duration
        while time.time() - start_time < RECORDING_DURATION:
            time.sleep(0.1)
            
            # Force a transcript display every second
            if int(time.time()) != int(time.time() - 0.1):
                current = audio_processor.get_transcript()
                if current:
                    print(f"\r🗣️  Current transcript: {current}")
    
    # Wait a bit for the final processing
    time.sleep(2)
    
    # Display final results
    final_transcript = audio_processor.get_final_transcript()
    print("\n\n📋 Final transcript:", final_transcript)
    
    # Show any messages added to the shared state
    print("\n💬 Messages in shared state:")
    for msg in shared_state.get_messages():
        print(f"  - {msg['role']}: {msg['content']}")
    
    # Cleanup
    audio_processor.close()
    print("\n✅ Test completed!")

if __name__ == "__main__":
    # Test the audio processor
    test_audio_processor()