import sounddevice as sd
import numpy as np

def list_audio_devices():
    """List all available audio input devices"""
    print("\nAvailable Audio Input Devices:")
    print("-" * 50)
    devices = sd.query_devices()
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:  # Only show input devices
            print(f"Device {i}: {device['name']}")
            print(f"    Input channels: {device['max_input_channels']}")
            print(f"    Default samplerate: {device['default_samplerate']}")
            print("-" * 50)
    
    print(f"\nDefault Input Device: {sd.query_devices(kind='input')}")

def test_microphone_levels(duration=5, device=None):
    """Test microphone input levels"""
    samplerate = 16000
    print(f"\nTesting microphone levels for {duration} seconds...")
    print("Please speak into your microphone...")
    
    # Record audio
    recording = sd.rec(int(samplerate * duration), samplerate=samplerate,
                      channels=1, dtype=np.float32, device=device)
    sd.wait()
    
    # Analyze levels
    max_amplitude = np.max(np.abs(recording))
    rms = np.sqrt(np.mean(recording**2))
    
    print(f"\nRecording Analysis:")
    print(f"Max amplitude: {max_amplitude:.4f}")
    print(f"RMS level: {rms:.4f}")
    
    if max_amplitude < 0.01:
        print("\nWARNING: Very low audio levels detected. Please check:")
        print("1. Is your microphone connected?")
        print("2. Is your microphone selected as the default input device?")
        print("3. Is your microphone muted?")
        print("4. Are your system input levels set correctly?")
    else:
        print("\nMicrophone appears to be working!")

if __name__ == "__main__":
    list_audio_devices()
    test_microphone_levels()
